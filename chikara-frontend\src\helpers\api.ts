import { orpc } from "@/lib/orpc";

export const api = {
    // Authentication Routes (better-auth)
    AUTH: {
        LOGIN: "/auth/login",
        REQPASSWORDRESET: "/auth/requestPasswordReset",
        PASSWORDRESET: "/auth/resetPassword",
        LOGOUT: "/auth/logout",
        // Registration Routes (ORPC)
        REGISTER: orpc.auth.register,
        CHECKUSERNAME: orpc.auth.checkUsername,
    },

    // Admin Routes
    ADMIN: {
        CHATBAN: orpc.admin.chatBanUser,
        HIDEMESSAGE: orpc.admin.hideSingleMessage,
        UNHIDEMESSAGE: orpc.admin.unhideSingleMessage,
        DELETEMESSAGE: orpc.admin.deleteSingleMessage,
        GANGINFO: orpc.admin.getGangInfo,
    },

    // User Routes
    USER: {
        FACULTYLIST: orpc.user.getUserList,
        CURRENTUSERINFO: orpc.user.getCurrentUserInfo,
        USERINFO: orpc.user.getUserInfo,
        INVENTORY: orpc.user.getInventory,
        EQUIPPEDITEMS: orpc.user.getEquippedItems,
        TRADEABLEINVENTORY: orpc.user.getTradeableInventory,
        STATUSEFFECTS: orpc.user.getStatusEffects,
        UPDATEPROFILEDETAILS: orpc.user.updateProfileDetails,
        TRAIN: orpc.user.train,
        EQUIP: orpc.user.equipItem,
        UNEQUIP: orpc.user.unequipItem,
        USEITEM: orpc.user.useItem,
        GAMECONFIG: orpc.user.getGameConfig,
        LINKDISCORD: orpc.user.linkDiscord,
        SETLASTNEWSIDREAD: orpc.user.setLastNewsIDRead,
        SKILLS: orpc.user.getSkills,
    },

    // Profile Routes
    PROFILE: {
        VIEWCOMMENTS: orpc.profileComment.getComments,
        CREATECOMMENT: orpc.profileComment.postComment,
    },

    // Registration Codes Routes (ORPC)
    REGISTRATION_CODES: {
        REFERRALCODELIST: orpc.auth.getReferralCodes,
        CHECKCODE: orpc.auth.checkCode,
    },

    // Gang Routes
    GANG: {
        GANGLIST: orpc.gang.getGangList,
        GANGINFO: orpc.gang.getGangInfo,
        CREATEGANG: orpc.gang.createGang,
        CURRENTGANGINFO: orpc.gang.getCurrentGang,
        MEMBERSHARES: orpc.gang.getMemberShares,
        GANGLOGS: orpc.gang.getGangLogs,
        GANGINVITELIST: orpc.gang.getInviteList,
        HASGANGSIGIL: orpc.gang.hasGangSigil,
        GANGINVITE: orpc.gang.inviteMember,
        CURRENTINVITES: orpc.gang.getCurrentInvites,
        ACCEPTINVITE: orpc.gang.acceptInvite,
        DECLINEINVITE: orpc.gang.declineInvite,
        ASSIGNRANK: orpc.gang.assignRank,
        UPDATESHARES: orpc.gang.updatePayoutShares,
        UPGRADEHIDEOUT: orpc.gang.upgradeHideout,
        LEAVEGANG: orpc.gang.leaveGang,
        REQUESTINVITE: orpc.gang.requestInvite,
        UPDATEGANGINFO: orpc.gang.updateGangInfo,
        KICKMEMBER: orpc.gang.kickMember,
    },

    // Items Routes
    ITEMS: {
        GETUPGRADEITEMS: orpc.item.getUpgradeItems,
        UPGRADEITEM: orpc.item.upgradeItem,
        GETITEMLIST: orpc.admin.item.list, // Dev only route
    },

    // Bank Routes
    BANK: {
        WITHDRAW: orpc.bank.withdraw,
        DEPOSIT: orpc.bank.deposit,
        TRANSFER: orpc.bank.transfer,
        BANKTRANSACTIONS: orpc.bank.getBankTransactions,
    },

    // Shops Routes
    SHOPS: {
        SHOPLIST: orpc.shop.shopList,
        SHOPINFO: orpc.shop.shopInfo,
        CREATESHOP: orpc.shop.admin.createShop,
        EDITSHOP: orpc.shop.admin.updateShop,
        DELETESHOP: orpc.shop.admin.deleteShop,
        CREATESHOPLISTING: orpc.shop.admin.createShopListing,
        EDITSHOPLISTING: orpc.shop.admin.editShopListing,
        DELETESHOPLISTING: orpc.shop.admin.deleteShopListing,
        PURCHASEITEM: orpc.shop.purchaseItem,
        SELLITEM: orpc.shop.sellItem,
        GETTRADERREP: orpc.shop.getTraderRep,
    },

    // Job Routes
    JOBS: {
        JOBLIST: orpc.job.list,
        CURRENTJOBINFO: orpc.job.info,
        APPLYFORJOB: orpc.job.apply,
        APPLYFORPROMOTION: orpc.job.promote,
        GETJOBLVLREQS: orpc.job.getRequirements,
        CHANGEPAYOUTTIME: orpc.job.changePayoutTime,
    },

    BATTLE: {
        BATTLEBEGIN: orpc.battle.begin,
        ATTACK: orpc.battle.attack,
        POSTBATTLEACTION: orpc.battle.postBattleAction,
        STATUS: orpc.battle.getStatus,
    },

    // Chat Routes
    CHAT: {
        HISTORY: orpc.chat.getHistory,
        ROOMS: orpc.chat.getRooms,
    },

    // Infirmary Routes
    INFIRMARY: {
        INFIRMARYLIST: orpc.infirmary.getHospitalList,
        INJUREDLIST: orpc.infirmary.getInjuredList,
        REVIVEPLAYER: orpc.infirmary.revivePlayer,
        CHECKIN: orpc.infirmary.hospitalCheckIn,
    },

    // Jail Routes
    JAIL: {
        JAILLIST: orpc.jail.jailList,
        JAILBAIL: orpc.jail.bail,
    },

    // Property Routes
    PROPERTY: {
        HOUSINGLIST: orpc.property.getHousingList,
        PURCHASE: orpc.property.purchaseProperty,
        SELL: orpc.property.sellProperty,
        SETPRIMARY: orpc.property.setPrimaryProperty,
    },

    // Crafting Routes
    CRAFTING: {
        GETQUEUE: orpc.crafting.getCraftingQueue,
        RECIPELIST: orpc.crafting.getRecipes,
        CRAFTITEM: orpc.crafting.craftItem,
        COMPLETECRAFT: orpc.crafting.completeCraft,
        CANCELCRAFT: orpc.crafting.cancelCraft,
    },

    // Private Messaging Routes
    MESSAGING: {
        HISTORY: orpc.privateMessage.getChatHistory,
        UNREAD: orpc.privateMessage.getUnreadCount,
        SENDMESSAGE: orpc.privateMessage.sendMessage,
        READMESSAGE: orpc.privateMessage.markMessageRead,
    },

    // Roguelike Routes
    ROGUELIKE: {
        CURRENTMAP: orpc.roguelike.getCurrentMap,
        BEGIN: orpc.roguelike.beginRun,
        ADVANCE: orpc.roguelike.advance,
        ACTIVATENODE: orpc.roguelike.activateNode,
        SCAVENGEOPTION: orpc.roguelike.chooseScavengeOption,
    },

    // Notifications Routes
    NOTIFICATIONS: {
        HISTORY: orpc.notification.getList,
        UNREAD: orpc.notification.getUnreadCount,
        READ: orpc.notification.markRead,
        UPDATEPUSHNOTIFICATIONSETTINGS: orpc.notification.updatePushSettings,
        SAVEFCMTOKEN: orpc.notification.saveFCMToken,
    },

    // leaderboards Routes
    LEADERBOARDS: {
        GETBOARDS: orpc.leaderboard.getLeaderBoards,
        GETEMOTEBOARDS: orpc.leaderboard.getChatEmoteLeaderboards,
    },

    // Courses/Dojo Routes
    COURSES: {
        COURSELIST: orpc.course.list,
        STARTCOURSE: orpc.course.start,
    },

    // Casino Routes
    CASINO: {
        SLOTS: orpc.casino.gamble,
        LOTTERY: orpc.casino.getLottery,
        ENTERLOTTERY: orpc.casino.enterLottery,
        CHECKENTRY: orpc.casino.checkLotteryEntry,
        ROULETTEBET: orpc.casino.placeBet,
    },

    // Special/Unique Items Routes
    SPECIALITEMS: {
        DEATHNOTE: orpc.item.useDeathNote,
        LIFENOTE: orpc.item.useLifeNote,
        KOMPROMAT: orpc.item.useKompromat,
        MEGAPHONE: orpc.item.useMegaphone,
        DAILYCHEST: orpc.item.useDailyChest,
        DAILYCHESTITEMS: orpc.item.getDailyChestItems,
        REDEEM_MATERIALS_CRATE: orpc.item.useMaterialsCrate,
        REDEEM_TOOLS_CRATE: orpc.item.useToolsCrate,
    },

    // Pets Routes
    PETS: {
        LIST: orpc.pets.list,
        FEED: orpc.pets.feed,
        PLAY: orpc.pets.play,
        TRAIN: orpc.pets.train,
        CUSTOMIZE: orpc.pets.customize,
        EVOLVE: orpc.pets.evolve,
        SETACTIVE: orpc.pets.setActive,
    },

    // Talents Routes
    TALENTS: {
        GETTALENTS: orpc.talents.getTalents,
        GETUNLOCKEDTALENTS: orpc.talents.getUnlockedTalents,
        UNLOCKTALENT: orpc.talents.unlockTalent,
        EQUIPABILITY: orpc.talents.equipAbility,
        UNEQUIPABILITY: orpc.talents.unequipAbility,
        RESETTALENTS: orpc.talents.resetTalents,
    },

    // Quests/Tasks Routes
    QUESTS: {
        GETQUESTPROGRESS: orpc.quest.getProgress,
        AVAILABLEQUESTS: orpc.quest.getAvailable,
        STARTQUEST: orpc.quest.start,
        COMPLETEQUEST: orpc.quest.complete,
        GETCOMBINEDQUESTLIST: orpc.quest.getCombinedList,
        ACTIVEQUESTS: orpc.quest.getActive,
        COMPLETEDQUESTS: orpc.quest.getCompleted,
        HANDINITEM: orpc.quest.handInItem,
    },

    // Bounty Routes
    BOUNTIES: {
        BOUNTYLIST: orpc.bounty.getBountyList,
        ACTIVEBOUNTYLIST: orpc.bounty.getActiveBountyList,
        PLACEBOUNTY: orpc.bounty.placeBounty,
        DELETEBOUNTY: orpc.bounty.deleteBounty,
    },

    // Suggestions Routes
    SUGGESTIONS: {
        SUGGESTIONLIST: orpc.suggestions.getSuggestions,
        VOTEHISTORY: orpc.suggestions.getVoteHistory,
        COMMENTS: orpc.suggestions.getComments,
        CREATESUGGESTION: orpc.suggestions.create,
        SUGGESTIONVOTE: orpc.suggestions.vote,
        SUGGESTIONCOMMENT: orpc.suggestions.comment,
        UPDATESUGGESTIONSTATE: orpc.suggestions.changeState,
        AVAILABLEPOLLS: orpc.suggestions.getAvailablePolls,
        CREATEPOLLRESPONSE: orpc.suggestions.submitPollResponse,
        POLLRESULTS: orpc.suggestions.getPollResults,
    },

    MISSIONS: {
        MISSIONLIST: orpc.mission.getList,
        STARTMISSION: orpc.mission.start,
        CANCELMISSION: orpc.mission.cancel,
        CURRENTMISSION: orpc.mission.getCurrent,
    },

    SHRINE: {
        DAILYGOAL: orpc.shrine.getGoal,
        GETDONATIONS: orpc.shrine.getDonations,
        DONATE: orpc.shrine.donate,
        ACTIVESHRINEBUFF: orpc.shrine.getActiveBuff,
        ISBUFFACTIVE: orpc.shrine.isBuffActive,
    },

    AUCTIONS: {
        AUCTIONLIST: orpc.auction.getList,
        CREATEAUCTIONLISTING: orpc.auction.createListing,
        BUYOUTLISTING: orpc.auction.buyoutListing,
        CANCELLISTING: orpc.auction.cancelListing,
    },

    ROOFTOP: {
        NPCLIST: orpc.battle.rooftopList,
        BEGIN: orpc.battle.beginRooftopBattle,
    },

    SOCIAL: {
        FRIENDLIST: orpc.social.getFriends,
        FRIENDREQUESTS: orpc.social.getFriendRequests,
        SENDFRIENDREQUEST: orpc.social.sendFriendRequest,
        RESPONDFRIENDREQUEST: orpc.social.respondToFriendRequest,
        REMOVEFRIEND: orpc.social.removeFriend,
        UPDATEFRIENDNOTE: orpc.social.updateFriendNote,
        UPDATESTATUSMESSAGE: orpc.social.updateStatusMessage,
        TOGGLEPRIVACYSETTINGS: orpc.social.updatePrivacySettings,
        GETRIVALLIST: orpc.social.getRivals,
        ADDRIVAL: orpc.social.addRival,
        REMOVERIVAL: orpc.social.removeRival,
        UPDATERIVALNOTE: orpc.social.updateRivalNote,
    },

    SCAVENGING: {
        GENERATE_GRID: orpc.skills.scavenging.generateGrid,
        REVEAL_CELL: orpc.skills.scavenging.revealCell,
        ACTIVE_SESSION: orpc.skills.scavenging.getActiveSession,
        DEV_GRID: orpc.skills.scavenging.devGrid,
        END_SESSION: orpc.skills.scavenging.endSession,
        RESET_GRID: orpc.skills.scavenging.resetGrid,
    },

    MINING: {
        START: orpc.skills.startMining,
        PROCESS_SWING: orpc.skills.processSwing,
        SESSION: orpc.skills.getMiningSession,
        CANCEL: orpc.skills.cancelMining,
    },

    // Explore Routes
    EXPLORE: {
        MAP: orpc.explore.getMapByLocation,
        INTERACT: orpc.explore.interactWithNode,
    },
};
